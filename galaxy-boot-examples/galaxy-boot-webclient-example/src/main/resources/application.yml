server:
  port: 8088

spring:
  application:
    name: galaxy-boot-webclient-example-service
  cloud:
    loadbalancer:
      enabled: true
    discovery:
      client:
        simple:
          instances:
            loadbalancer-service:
              - uri: http://localhost:8089
    apollo:
      enabled: false  # 禁用Apollo配置中心
      meta: ""        # 清空meta地址
      bootstrap:
        enabled: false # 禁用bootstrap模式
  profiles:
    active: default
  autoconfigure:
    exclude:
      - com.ctrip.framework.apollo.spring.boot.ApolloAutoConfiguration

galaxy:
  webclient:
    esb:
      user: user
      password: password

  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: false
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true

logging:
  level:
    cn.com.chinastock.cnf.webclient.filter: INFO

apollo:
  bootstrap:
    enabled: false
  meta: ""
  enabled: false
  # 添加更多禁用配置
  config-service-url: ""
  admin-service-url: ""
  app-id: ""
  cluster: ""
  namespace: ""