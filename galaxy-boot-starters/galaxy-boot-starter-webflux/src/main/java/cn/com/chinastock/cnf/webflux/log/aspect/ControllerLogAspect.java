package cn.com.chinastock.cnf.webflux.log.aspect;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.webflux.log.logger.RequestLogger;
import cn.com.chinastock.cnf.webflux.log.logger.ResponseLogger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.context.ContextView;

import java.lang.annotation.Annotation;

/**
 * ControllerLogAspect 类是WebFlux环境下的控制器日志切面。
 * 该切面用于在控制器方法执行前后记录请求和响应的日志信息，支持敏感字段掩码处理。
 *
 * <p>该切面的主要功能包括：</p>
 * <ul>
 *     <li>拦截RestController注解标记的控制器方法</li>
 *     <li>记录请求体日志，支持敏感字段掩码</li>
 *     <li>记录响应体日志，支持敏感字段掩码</li>
 *     <li>支持Mono和Flux响应式类型的日志记录</li>
 *     <li>与Reactor上下文集成，获取ServerWebExchange信息</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Aspect
public class ControllerLogAspect {
    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private LogProperties logProperties;

    /**
     * 构造函数，初始化日志配置
     *
     * @param logProperties 日志配置属性
     */
    public ControllerLogAspect(LogProperties logProperties) {
        this.logProperties = logProperties;
    }

    /**
     * 定义控制器切点
     * 匹配所有标记了@RestController注解的类中的方法
     */
    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void controllerPointcut() {
    }

    /**
     * 处理日志记录
     * 在控制器方法执行前后记录请求和响应日志
     *
     * @param joinPoint 连接点对象
     * @return 方法执行结果
     * @throws Throwable 方法执行过程中可能抛出的异常
     */
    @Around("controllerPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        if (!logProperties.isControllerLogEnabled()) {
            return joinPoint.proceed();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();

        Object[] originalArgs = joinPoint.getArgs();
        Object[] newArgs = new Object[originalArgs.length];
        Annotation[][] parameterAnnotations = signature.getMethod().getParameterAnnotations();

        // 处理请求参数并记录请求日志
        boolean hasRequestBodyLogged = false;
        for (int i = 0; i < originalArgs.length; i++) {
            newArgs[i] = logRequestBody(originalArgs[i], parameterAnnotations[i]);
            // 检查是否已经记录了请求体日志
            if (isAnnotationPresent(parameterAnnotations[i], RequestBody.class)) {
                hasRequestBodyLogged = true;
            }
        }

        Object result = joinPoint.proceed(newArgs);

        // 处理响应日志记录
        if (result instanceof Mono) {
            return hasRequestBodyLogged ?
                    logResponseWithContext((Mono<?>) result) :
                    logBasicRequestAndResponseWithContext((Mono<?>) result);
        }
        if (result instanceof Flux) {
            Mono<?> listMono = ((Flux<?>) result).collectList();
            return (hasRequestBodyLogged ?
                    logResponseWithContext(listMono) :
                    logBasicRequestAndResponseWithContext(listMono))
                    .flatMapMany(list -> Flux.fromIterable((Iterable<?>) list));
        }
        logger.warn(LogCategory.FRAMEWORK_LOG, "ignore response log when return void or return non Mono|Flux.");

        return result;

    }

    /**
     * 记录请求体日志
     * 如果参数标记了@RequestBody注解，则记录其日志
     *
     * @param originalArg 原始参数
     * @param annotations 参数注解数组
     * @return 处理后的参数
     */
    private Object logRequestBody(Object originalArg, Annotation[] annotations) {
        if (isAnnotationPresent(annotations, RequestBody.class)) {
            if (originalArg instanceof Mono) {
                return logRequestWithContext((Mono<?>) originalArg);
            }
            if (originalArg instanceof Flux) {
                return logRequestWithContext(((Flux<?>) originalArg).collectList()).flatMapMany(Flux::fromIterable);
            }
            logPlainRequestBody(originalArg);
        }
        return originalArg;
    }

    /**
     * 记录普通对象的请求体日志
     * 对于普通对象，我们使用RequestLogger.logSensitive方法直接记录日志
     *
     * @param requestBody 请求体对象
     */
    private void logPlainRequestBody(Object requestBody) {
        Mono.deferContextual(contextView -> {
            logRequest(contextView, requestBody);
            return Mono.empty();
        }).subscribe();
    }

    /**
     * 检查注解数组中是否包含指定类型的注解
     *
     * @param annotations      注解数组
     * @param targetAnnotation 目标注解类型
     * @return true表示包含，false表示不包含
     */
    private boolean isAnnotationPresent(Annotation[] annotations, Class<? extends Annotation> targetAnnotation) {
        for (Annotation annotation : annotations) {
            if (targetAnnotation.isInstance(annotation)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 在Reactor上下文中记录响应日志
     *
     * @param result 响应结果的Mono对象
     * @param <T>    响应类型
     * @return 包含日志记录的Mono对象
     */
    private <T> Mono<T> logResponseWithContext(Mono<T> result) {
        return result.transformDeferredContextual((mono, contextView) ->
                mono.doOnNext(body -> {
                    logResponse(contextView, body);
                    logPerformance(contextView);
                })
        );
    }

    /**
     * 从Reactor上下文中获取ServerWebExchange对象
     *
     * @param contextView Reactor上下文视图
     * @return ServerWebExchange对象
     */
    private static ServerWebExchange getServerWebExchange(ContextView contextView) {
        return contextView.get("SERVER_WEB_EXCHANGE");
    }

    /**
     * 在Reactor上下文中记录请求日志
     *
     * @param arg 请求参数的Mono对象
     * @param <T> 请求类型
     * @return 包含日志记录的Mono对象
     */
    private <T> Mono<T> logRequestWithContext(Mono<T> arg) {
        return arg.transformDeferredContextual((mono, contextView) ->
                mono.doOnNext(body -> logRequest(contextView, body))
        );
    }

    /**
     * 在Reactor上下文中记录基本请求日志和响应日志
     * 用于没有@RequestBody参数的请求，如GET请求等
     * 使用doFirst确保请求日志在业务逻辑执行前记录
     * 使用doOnNext和doFinally确保响应日志在有数据时记录，在无数据时也能记录基本响应信息
     *
     * @param result 响应结果的Mono对象
     * @param <T>    响应类型
     * @return 包含日志记录的Mono对象
     */
    private <T> Mono<T> logBasicRequestAndResponseWithContext(Mono<T> result) {
        return result.transformDeferredContextual((mono, contextView) -> {
            final boolean[] responseLogged = {false}; // 标记响应日志是否已记录

            return mono.doFirst(() -> {
                        // 在业务逻辑执行前记录基本请求日志（无请求体）
                        logRequest(contextView, null);
                    })
                    .doOnNext(body -> {
                        // 记录响应日志（有响应体时）
                        try {
                            ResponseLogger.logSensitive(
                                    getServerWebExchange(contextView).getResponse(),
                                    logProperties.isResponseHeadersEnabled(),
                                    body
                            );
                            responseLogged[0] = true;
                        } catch (Exception e) {
                            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to log response: " + e.getMessage());
                        }
                    })
                    .doFinally(signalType -> {
                        // 如果响应日志还没有记录，记录基本响应信息（无响应体或空响应体时）
                        if (!responseLogged[0]) {
                            logResponse(contextView, null);
                        }

                        logPerformance(contextView);
                    });
        });
    }

    private void logRequest(ContextView contextView, Object body) {
        try {
            ServerWebExchange exchange = getServerWebExchange(contextView);
            RequestLogger.logSensitive(
                    exchange.getRequest(),
                    logProperties.isRequestHeadersEnabled(),
                    body
            );
        } catch (Exception e) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to log basic request: " + e.getMessage());
        }
    }

    private void logResponse(ContextView contextView, Object body) {
        try {
            ResponseLogger.logSensitive(
                    getServerWebExchange(contextView).getResponse(),
                    logProperties.isResponseHeadersEnabled(),
                    body
            );
        } catch (Exception e) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to log response: " + e.getMessage());
        }
    }

    private void logPerformance(ContextView contextView) {
        if (logProperties.isPerformanceLogEnabled()) {
            try {
                Long requestStartTime = contextView.getOrDefault("REQUEST_START_TIME", null);
                if (requestStartTime != null) {
                    logger.info(LogCategory.PERFORMANCE_LOG, "cost=" + (System.currentTimeMillis() - requestStartTime) + " unit=ms");
                } else {
                    logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to log performance: request start time not found");
                }
            } catch (Exception e) {
                logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to log performance: " + e.getMessage());
            }
        }
    }

}